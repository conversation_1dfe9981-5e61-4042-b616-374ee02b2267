// Service Worker for Push Notifications - TEMPORARILY DISABLED FOR M<PERSON>BILE TOUCH DEBUGGING
console.log('Service Worker disabled for mobile touch debugging')

/* TEMPORARILY DISABLED
self.addEventListener('push', function(event) {
  if (event.data) {
    const data = event.data.json()
    
    const options = {
      body: data.body,
      icon: '/web-app-manifest-192x192.png',
      badge: '/favicon-96x96.png',
      data: data.data,
      actions: [
        {
          action: 'view',
          title: 'View'
        },
        {
          action: 'close',
          title: 'Close'
        }
      ],
      requireInteraction: true,
      tag: data.tag || 'onlydiary-notification'
    }

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

self.addEventListener('notificationclick', function(event) {
  event.notification.close()

  if (event.action === 'view' || !event.action) {
    // Open the app to the relevant page
    const urlToOpen = event.notification.data?.url || '/'
    
    event.waitUntil(
      clients.matchAll({
        type: 'window',
        includeUncontrolled: true
      }).then(function(clientList) {
        // Check if app is already open
        for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i]
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            client.focus()
            client.navigate(urlToOpen)
            return
          }
        }
        
        // Open new window if app not open
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen)
        }
      })
    )
  }
})

self.addEventListener('notificationclose', function(event) {
  // Track notification close if needed
  console.log('Notification closed:', event.notification.tag)
})

// Cache management for offline functionality
const CACHE_NAME = 'onlydiary-v1'
const urlsToCache = [
  '/',
  '/offline',
  '/site.webmanifest',
  '/favicon.ico',
  '/web-app-manifest-192x192.png',
  '/web-app-manifest-512x512.png'
]

// Install event - cache resources
self.addEventListener('install', function(event) {
  console.log('Service Worker installing...')
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Service Worker caching resources')
        return cache.addAll(urlsToCache)
      })
      .then(() => {
        console.log('Service Worker installed successfully')
        return self.skipWaiting()
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
  console.log('Service Worker activating...')
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      console.log('Service Worker activated successfully')
      return self.clients.claim()
    })
  )
})

// Fetch event - serve from cache when offline
self.addEventListener('fetch', function(event) {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return

  // Skip external requests
  if (!event.request.url.startsWith(self.location.origin)) return

  // Skip Next.js internal files (static chunks, HMR, etc.)
  if (event.request.url.includes('/_next/')) return

  // Skip API routes
  if (event.request.url.includes('/api/')) return

  // Don't intercept requests that might interfere with touch events
  if (event.request.url.includes('touch') ||
      event.request.url.includes('gesture') ||
      event.request.url.includes('interaction')) return

  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Return cached version or fetch from network
        return response || fetch(event.request).catch(function() {
          // If both cache and network fail, show offline page for navigation requests
          if (event.request.mode === 'navigate') {
            return caches.match('/offline')
          }
        })
      })
  )
})
*/
