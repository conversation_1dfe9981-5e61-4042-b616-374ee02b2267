'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
// import { subscribeToPush, isSubscribedToPush, isPushSupported } from '@/lib/notifications/push'
import { createSupabaseClient } from '@/lib/supabase/client'

export default function TestPWAPage() {
  // Temporarily disabled for mobile touch debugging
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">PWA Test Page</h1>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-gray-600">
            PWA testing is temporarily disabled while debugging mobile touch issues.
          </p>
        </div>
      </div>
    </div>
  )

  /* TEMPORARILY DISABLED
  const [isSupported, setIsSupported] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [swRegistered, setSwRegistered] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [error, setError] = useState<string>('')
  const [userAgent, setUserAgent] = useState<string>('')
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)

  /*
  useEffect(() => {
    const supabase = createSupabaseClient()

    // Get current user
    supabase.auth.getUser().then(({ data: { user } }) => {
      setCurrentUser(user)
    })

    // Set user agent safely
    setUserAgent(navigator.userAgent)

    // Check PWA support
    setIsSupported(isPushSupported())

    // Check if installed as PWA
    setIsInstalled(window.matchMedia('(display-mode: standalone)').matches)

    // Check service worker with a small delay to ensure it's registered
    const checkServiceWorker = () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(reg => {
          setSwRegistered(!!reg)
        })
      }
    }

    // Check immediately and again after a delay
    checkServiceWorker()
    setTimeout(checkServiceWorker, 1000)

    // Check push subscription
    isSubscribedToPush().then(setIsSubscribed)
  }, [])

  const handleSubscribe = async () => {
    if (!currentUser) {
      setError('You must be logged in to subscribe to push notifications')
      return
    }

    setError('')
    try {
      const success = await subscribeToPush(currentUser.id)
      if (success) {
        setIsSubscribed(true)
        alert('Successfully subscribed to push notifications!')
      } else {
        setError('Failed to subscribe to push notifications')
      }
    } catch (error: any) {
      console.error('Error subscribing:', error)
      setError(`Error subscribing: ${error.message}`)
    }
  }

  const refreshStatus = () => {
    // Re-check all statuses
    setIsSupported(isPushSupported())
    setIsInstalled(window.matchMedia('(display-mode: standalone)').matches)

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(reg => {
        setSwRegistered(!!reg)
      })
    }

    isSubscribedToPush().then(setIsSubscribed)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">PWA Test Page</h1>
        
        <div className="bg-white rounded-lg shadow p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            {/* PWA Installation Status */}
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">PWA Installation</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Installed as PWA:</span>
                  <span className={isInstalled ? 'text-green-600' : 'text-red-600'}>
                    {isInstalled ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Service Worker:</span>
                  <span className={swRegistered ? 'text-green-600' : 'text-red-600'}>
                    {swRegistered ? '✅ Registered' : '❌ Not Registered'}
                  </span>
                </div>
              </div>
            </div>

            {/* Push Notifications Status */}
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">Push Notifications</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Browser Support:</span>
                  <span className={isSupported ? 'text-green-600' : 'text-red-600'}>
                    {isSupported ? '✅ Supported' : '❌ Not Supported'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Subscribed:</span>
                  <span className={isSubscribed ? 'text-green-600' : 'text-red-600'}>
                    {isSubscribed ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
              </div>
            </div>

          </div>

          {/* Test Actions */}
          <div className="border-t pt-6">
            <h3 className="font-semibold text-gray-800 mb-4">Test Actions</h3>
            <div className="space-y-3">

              {/* User Status */}
              <div className="text-sm">
                <span className="font-medium">User Status: </span>
                <span className={currentUser ? 'text-green-600' : 'text-red-600'}>
                  {currentUser ? `✅ Logged in as ${currentUser.email}` : '❌ Not logged in'}
                </span>
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-sm text-red-700">
                  {error}
                </div>
              )}

              {!isSubscribed && isSupported && (
                <Button
                  onClick={handleSubscribe}
                  className="w-full"
                  disabled={!currentUser}
                >
                  Subscribe to Push Notifications
                </Button>
              )}

              {!currentUser && (
                <div className="text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <strong>Note:</strong> You need to be logged in to test push notifications.
                  <a href="/login" className="text-blue-600 hover:underline ml-1">Login here</a>
                </div>
              )}

              <div className="space-y-2">
                <Button
                  onClick={refreshStatus}
                  variant="outline"
                  className="w-full"
                >
                  🔄 Refresh Status
                </Button>

                <Button
                  onClick={() => {
                    localStorage.setItem('pwa-installed', 'true')
                    localStorage.setItem('pwa-install-prompted', 'true')
                    refreshStatus()
                  }}
                  variant="outline"
                  className="w-full text-xs"
                >
                  🧪 Mark as Installed (Test)
                </Button>

                <Button
                  onClick={() => {
                    localStorage.removeItem('pwa-installed')
                    localStorage.removeItem('pwa-install-prompted')
                    localStorage.removeItem('pwa-manual-install-shown')
                    refreshStatus()
                  }}
                  variant="outline"
                  className="w-full text-xs"
                >
                  🔄 Reset PWA State (Test)
                </Button>
              </div>

              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>To test PWA installation:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Open this site in Chrome on mobile or desktop</li>
                  <li>Look for the install prompt or "Add to Home Screen"</li>
                  <li>Install the app and reopen this page</li>
                  <li>The "Installed as PWA" should show ✅ Yes</li>
                </ol>
              </div>

              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>To test push notifications:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Click "Subscribe to Push Notifications" above</li>
                  <li>Allow notifications when prompted</li>
                  <li>The "Subscribed" status should show ✅ Yes</li>
                </ol>
              </div>

            </div>
          </div>

          {/* Environment Info */}
          <div className="border-t pt-6">
            <h3 className="font-semibold text-gray-800 mb-2">Environment Info</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>User Agent: {userAgent}</div>
              <div>VAPID Key Present: {process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY ? '✅ Yes' : '❌ No'}</div>
            </div>
          </div>

        </div>
      </div>
    </div>
  )
  */
}
