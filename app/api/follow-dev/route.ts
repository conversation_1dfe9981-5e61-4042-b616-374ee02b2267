import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"

// Endpoint to follow/unfollow writers
export async function POST(request: NextRequest) {
  try {
    const { writerId, action } = await request.json()
    
    if (!writerId) {
      return NextResponse.json(
        { error: "Writer ID is required" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Check if user exists (any role can be followed in unified system)
    const { data: writer, error: writerError } = await supabase
      .from("users")
      .select("id, name")
      .eq("id", writerId)
      .single()

    if (writerError || !writer) {
      return NextResponse.json(
        { error: "Writer not found" },
        { status: 404 }
      )
    }

    // Check if already following
    const { data: existingFollow } = await supabase
      .from("follows")
      .select("id")
      .eq("follower_id", user.id)
      .eq("writer_id", writerId)
      .single()

    if (action === 'unfollow' || existingFollow) {
      // Unfollow
      const { error: unfollowError } = await supabase
        .from("follows")
        .delete()
        .eq("follower_id", user.id)
        .eq("writer_id", writerId)

      if (unfollowError) {
        console.error("Error unfollowing:", unfollowError)
        return NextResponse.json(
          { error: "Failed to unfollow user" },
          { status: 500 }
        )
      }

      // Follower count is automatically updated by database trigger

      return NextResponse.json({
        success: true,
        isFollowing: false,
        message: `Unfollowed ${writer.name}`
      })
    }

    // Create follow relationship
    const { data: follow, error: followError } = await supabase
      .from("follows")
      .insert({
        follower_id: user.id,
        writer_id: writerId
      })
      .select()
      .single()

    if (followError) {
      console.error("Error creating follow:", followError)
      return NextResponse.json(
        { error: "Failed to follow user" },
        { status: 500 }
      )
    }

    // Follower count is automatically updated by database trigger

    return NextResponse.json({
      success: true,
      isFollowing: true,
      follow,
      message: `Successfully followed ${writer.name}!`
    })

  } catch (error) {
    console.error("Error in follow-dev:", error)
    return NextResponse.json(
      { error: "Failed to follow user" },
      { status: 500 }
    )
  }
}