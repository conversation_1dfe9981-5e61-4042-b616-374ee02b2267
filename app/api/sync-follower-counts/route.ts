import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"

// Endpoint to manually sync all follower counts
export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated and is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from("users")
      .select("role")
      .eq("id", user.id)
      .single()

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    // Call the sync function
    const { data: result, error: syncError } = await supabase
      .rpc('sync_all_follower_counts')

    if (syncError) {
      console.error("Error syncing follower counts:", syncError)
      return NextResponse.json(
        { error: "Failed to sync follower counts" },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true,
      message: result || "Follower counts synced successfully"
    })

  } catch (error) {
    console.error("Error in sync-follower-counts:", error)
    return NextResponse.json(
      { error: "Failed to sync follower counts" },
      { status: 500 }
    )
  }
}

// GET endpoint to check follower count status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Get follower count statistics
    const { data: stats, error } = await supabase
      .from('users')
      .select('id, name, follower_count')
      .order('follower_count', { ascending: false })
      .limit(10)

    if (error) {
      console.error("Error getting follower stats:", error)
      return NextResponse.json(
        { error: "Failed to get follower statistics" },
        { status: 500 }
      )
    }

    // Get total counts
    const { data: totals } = await supabase
      .from('users')
      .select('follower_count')

    const totalUsers = totals?.length || 0
    const totalFollowers = totals?.reduce((sum, user) => sum + (user.follower_count || 0), 0) || 0
    const usersWithFollowers = totals?.filter(user => (user.follower_count || 0) > 0).length || 0

    return NextResponse.json({
      success: true,
      statistics: {
        totalUsers,
        totalFollowers,
        usersWithFollowers,
        topUsers: stats
      }
    })

  } catch (error) {
    console.error("Error getting follower stats:", error)
    return NextResponse.json(
      { error: "Failed to get follower statistics" },
      { status: 500 }
    )
  }
}
