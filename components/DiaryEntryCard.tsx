"use client"

import Link from "next/link"
import Image from "next/image"
import { VideoThumbnail } from "@/components/VideoThumbnail"
import { ReactionSystem } from "@/components/ReactionSystem"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  view_count?: number
  photos?: Array<{ url: string; alt_text?: string }>
  videos?: Array<{ r2_public_url: string; custom_thumbnail_url?: string }>
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface User {
  id: string
  name: string
  avatar?: string
  profile_picture_url?: string
}

interface DiaryEntryCardProps {
  post: DiaryEntry
  user: User
  currentUserId?: string
  formatDate: (date: string) => string
}

export function DiaryEntryCard({ post, user, currentUserId, formatDate }: DiaryEntryCardProps) {
  return (
    <div className="bg-white border-b border-gray-100 hover:bg-gray-50 transition-colors">
      <Link href={`/d/${post.id}`} className="block p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2 line-clamp-2 leading-tight">
          {post.title}
        </h3>
        
        <p className="text-gray-600 line-clamp-3 mb-4 leading-relaxed">
          {post.body_md
            .replace(/[#*`_~]/g, '')
            .replace(/\n/g, ' ')
            .substring(0, 180)
            .trim()}
          {post.body_md.length > 180 && '...'}
        </p>

        {post.photos && post.photos.length > 0 && (
          <div className="mb-4 -mx-4">
            <Image
              src={post.photos[0].url}
              alt={post.photos[0].alt_text || post.title}
              width={400}
              height={240}
              className="w-full h-48 object-cover"
            />
          </div>
        )}

        {post.videos && post.videos.length > 0 && (
          <div className="mb-4 -mx-4">
            <VideoThumbnail
              videoUrl={post.videos[0].r2_public_url}
              customThumbnailUrl={post.videos[0].custom_thumbnail_url}
              alt={post.title}
              className="w-full h-48 object-cover"
              timeInSeconds={1}
              showPlayButton={true}
              playButtonSize="lg"
            />
          </div>
        )}

        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            <span>{formatDate(post.created_at)}</span>
            <span>{post.body_md.split(' ').length} words</span>
            {post.view_count && post.view_count > 0 && <span>{post.view_count} views</span>}
          </div>
          {!post.is_free && (
            <span className="text-purple-600 font-medium">Premium</span>
          )}
        </div>
      </Link>

      <div className="px-4 pb-3">
        <ReactionSystem
          contentId={post.id}
          contentType="diary"
          currentUserId={currentUserId}
          initialReactions={post.reactions || {}}
          userReaction={post.userReaction}
          onReactionUpdate={() => {}}
        />
      </div>
    </div>
  )
}
