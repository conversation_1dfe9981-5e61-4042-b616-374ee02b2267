'use client'

import { useEffect } from 'react'

/**
 * Component to debug webpack originalFactory.call errors
 * This will help us identify exactly which module is failing
 */
export function WebpackErrorDebugger() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Capture webpack require errors
    const originalRequire = window.__webpack_require__
    if (originalRequire) {
      window.__webpack_require__ = function(moduleId) {
        try {
          return originalRequire.call(this, moduleId)
        } catch (error) {
          console.error('🚨 Webpack module load failed:', {
            moduleId,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
          })
          
          // Try to get more info about the failing module
          if (window.__webpack_require__.cache) {
            const moduleInfo = window.__webpack_require__.cache[moduleId]
            console.error('📦 Module info:', moduleInfo)
          }
          
          throw error
        }
      }
      
      // Copy over all properties
      Object.keys(originalRequire).forEach(key => {
        window.__webpack_require__[key] = originalRequire[key]
      })
    }

    // Global error handler for unhandled webpack errors
    const handleError = (event) => {
      if (event.error && event.error.message && 
          event.error.message.includes('originalFactory.call')) {
        console.error('🎯 Found originalFactory.call error:', {
          message: event.error.message,
          stack: event.error.stack,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        })
        
        // Don't prevent the error, just log it
        return false
      }
    }

    window.addEventListener('error', handleError)
    
    // Also catch unhandled promise rejections
    const handleRejection = (event) => {
      if (event.reason && event.reason.message && 
          event.reason.message.includes('originalFactory.call')) {
        console.error('🎯 Found originalFactory.call in promise rejection:', {
          reason: event.reason.message,
          stack: event.reason.stack,
          timestamp: new Date().toISOString()
        })
      }
    }

    window.addEventListener('unhandledrejection', handleRejection)

    // Cleanup
    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleRejection)
      
      // Restore original require if we modified it
      if (originalRequire) {
        window.__webpack_require__ = originalRequire
      }
    }
  }, [])

  // This component doesn't render anything
  return null
}
