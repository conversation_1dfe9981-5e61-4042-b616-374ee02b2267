'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { MailingListButton } from "@/components/MailingListButton"
import { PaywallContent } from "@/components/PaywallContent"
import { StoryMap } from "@/components/StoryMap"
import { VideoThumbnail } from "@/components/VideoThumbnail"
import { createSupabaseClient } from "@/lib/supabase/client"
import RecommendationModal from "@/components/RecommendationModal"
import { WriteMessageModal } from "@/components/WriteMessageModal"
import { AudioPost } from "@/components/AudioPost"
import { Day1Badge } from "@/components/Day1Badge"
import { ShareButton } from "@/components/ShareButton"
import { CompactCommentsSection } from "@/components/CompactCommentsSection"
import { CompactBookCommentsSection } from "@/components/CompactBookCommentsSection"
import { NavigationButton } from "@/components/NavigationButton"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
  view_count?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title: string; view_count: number; custom_thumbnail_url?: string }>
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  sales_count?: number
  bestseller_rank?: number
  created_at: string
}

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface UnifiedProfileClientProps {
  user: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  audioPosts: AudioPost[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
  currentUserId?: string
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// Helper function to format large numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
  }
  return num.toString()
}

export function UnifiedProfileClient({
  user,
  diaryEntries,
  projects,
  audioPosts,
  hasActiveSubscription,
  isFollowing,
  isOwnProfile,
  currentUserId
}: UnifiedProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'all' | 'diary' | 'books' | 'audio'>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest')
  const [loading, setLoading] = useState(false)
  const [recommendedCreators, setRecommendedCreators] = useState<any[]>([])
  const [entriesWithPhotos, setEntriesWithPhotos] = useState<DiaryEntry[]>(diaryEntries)
  const [showRecommendationModal, setShowRecommendationModal] = useState(false)
  const [showWriteMessageModal, setShowWriteMessageModal] = useState(false)
  const supabase = createSupabaseClient()
  const router = useRouter()

  // Load photos and videos for diary entries
  useEffect(() => {
    const loadMediaData = async () => {
      const entriesWithMediaData = await Promise.all(
        diaryEntries.map(async (entry) => {
          // Load photos
          const { data: photos } = await supabase
            .from('photos')
            .select('id, url, alt_text')
            .eq('diary_entry_id', entry.id)
            .eq('moderation_status', 'approved')
            .order('created_at', { ascending: true })

          // Load videos
          const { data: videos, error: videoError } = await supabase
            .from('videos')
            .select('id, r2_public_url, title, view_count, custom_thumbnail_url')
            .eq('post_id', entry.id)
            .order('created_at', { ascending: true })

          if (videoError) {
            console.error('Error loading videos for entry', entry.id, ':', videoError)
          }

          const entryWithMedia = {
            ...entry,
            photos: photos || [],
            videos: videos || []
          }

          // Debug logging for videos
          if (videos && videos.length > 0) {
            console.log(`Entry ${entry.id} has ${videos.length} videos:`, videos)
          }

          return entryWithMedia
        })
      )

      setEntriesWithPhotos(entriesWithMediaData)
    }

    loadMediaData()
  }, [diaryEntries, supabase])

  // Check if user has monetization set up
  const hasMonetizationSetup = user?.price_monthly && user?.stripe_account_id

  // Fetch favorite creators (user's actual recommendations)
  useEffect(() => {
    const fetchFavoriteCreators = async () => {
      try {
        const { data, error } = await supabase
          .from('favorite_creators')
          .select(`
            writer_id,
            users!favorite_creators_writer_id_fkey (
              id, name, bio, profile_picture_url, avatar, subscriber_count, entry_count
            )
          `)
          .eq('user_id', user.id)
          .limit(4)

        if (data && !error) {
          const creators = data.map(item => item.users).filter(Boolean)
          setRecommendedCreators(creators)
        }
      } catch (error) {
        console.error('Error fetching favorite creators:', error)
      }
    }

    if (user?.id) {
      fetchFavoriteCreators()
    }
  }, [user?.id, supabase, showRecommendationModal])

  // Safety checks
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">😕</div>
          <h2 className="text-xl font-serif text-gray-800 mb-2">Profile not found</h2>
          <p className="text-gray-600">This user doesn't exist or their profile is private.</p>
        </div>
      </div>
    )
  }

  return (
    <>
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Profile Header */}
        <div className="bg-white rounded-xl shadow-sm p-8 mb-6 relative">
          {/* Edit Profile Button - Top Right for Own Profile */}
          {isOwnProfile && (
            <Link
              href="/profile/edit"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-sm font-medium transition-colors flex items-center gap-1"
            >
              ⚙️ Edit
            </Link>
          )}

          <div className="flex flex-col lg:flex-row gap-8">

            {/* Avatar */}
            <div className="flex-shrink-0 mx-auto lg:mx-0">
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg">
                {user.profile_picture_url || user.avatar ? (
                  <img
                    src={user.profile_picture_url || user.avatar}
                    alt={user.name || 'Profile'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <span className="text-4xl font-serif text-gray-600">
                    {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                  </span>
                )}
              </div>
            </div>

            {/* Profile Info & Actions */}
            <div className="flex-1 text-center lg:text-left">

              {/* Name & Bio */}
              <div className="mb-6">
                <div className="flex items-center justify-center lg:justify-start gap-2 mb-3 flex-wrap">
                  <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 text-center lg:text-left">
                    {user.name || user.email || 'Anonymous User'}
                  </h1>
                  {user.has_day1_badge && (
                    <Day1Badge
                      signupNumber={user.signup_number}
                      badgeTier={user.badge_tier}
                      size="sm"
                      className="flex-shrink-0"
                    />
                  )}
                </div>
                {user.bio && (
                  <p className="text-gray-600 text-lg leading-relaxed max-w-2xl">
                    {user.bio}
                  </p>
                )}
              </div>

              {/* Stats - Responsive Design */}
              <div className="flex justify-center lg:justify-start mb-6">
                {/* Mobile: Show only 3 most important stats */}
                <div className="flex items-center gap-2 sm:gap-6 sm:hidden">
                  <div className="text-center">
                    <div className="text-base sm:text-xl font-bold text-gray-800">{formatNumber(user.follower_count || 0)}</div>
                    <div className="text-xs text-gray-500">Followers</div>
                  </div>
                  <div className="w-px h-6 sm:h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-base sm:text-xl font-bold text-gray-800">{formatNumber(diaryEntries.length + audioPosts.length + projects.length)}</div>
                    <div className="text-xs text-gray-500">Posts</div>
                  </div>
                  <div className="w-px h-6 sm:h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-base sm:text-xl font-bold text-gray-800">
                      {formatNumber(
                        diaryEntries.reduce((total, entry) => {
                          return total + (entry.body_md?.split(' ').length || 0)
                        }, 0) + audioPosts.reduce((total, post) => {
                          return total + (post.description?.split(' ').length || 0)
                        }, 0) + projects.reduce((total, project) => {
                          return total + (project.total_words || 0)
                        }, 0)
                      )}
                    </div>
                    <div className="text-xs text-gray-500">Words</div>
                  </div>
                </div>

                {/* Desktop: Show all stats */}
                <div className="hidden sm:flex items-center gap-6">
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(user.follower_count || 0)}</div>
                    <div className="text-sm text-gray-500">Followers</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(diaryEntries.length)}</div>
                    <div className="text-sm text-gray-500">Entries</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(projects.length)}</div>
                    <div className="text-sm text-gray-500">Books</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(audioPosts.length)}</div>
                    <div className="text-sm text-gray-500">Audio</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">
                      {formatNumber(
                        activeTab === 'all'
                          ? diaryEntries.reduce((total, entry) => {
                              return total + (entry.body_md?.split(' ').length || 0)
                            }, 0) + audioPosts.reduce((total, post) => {
                              return total + (post.description?.split(' ').length || 0)
                            }, 0) + projects.reduce((total, project) => {
                              return total + (project.total_words || 0)
                            }, 0)
                          : activeTab === 'diary'
                          ? diaryEntries.reduce((total, entry) => {
                              return total + (entry.body_md?.split(' ').length || 0)
                            }, 0)
                          : activeTab === 'books'
                          ? projects.reduce((total, project) => {
                              return total + (project.total_words || 0)
                            }, 0)
                          : audioPosts.reduce((total, post) => {
                              return total + (post.description?.split(' ').length || 0)
                            }, 0)
                      )}
                    </div>
                    <div className="text-sm text-gray-500">{activeTab === 'audio' ? 'Description Words' : 'Words'}</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {isOwnProfile ? (
                  // Own Profile Actions
                  <>
                    {/* Primary Create Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <NavigationButton
                        href="/write"
                        className="bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                      >
                        ✍️ Write Entry
                      </NavigationButton>
                      <NavigationButton
                        href="/publishing"
                        className="bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors text-center"
                      >
                        📚 Publish Book
                      </NavigationButton>
                    </div>
                  </>
                ) : (
                  // Visitor Actions - Follow is primary
                  <>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start w-full">
                      <FollowButton
                        writerId={user.id}
                        writerName={user.name}
                        initialIsFollowing={isFollowing}
                      />

                      <button
                        onClick={() => setShowWriteMessageModal(true)}
                        className="bg-amber-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-amber-700 transition-colors text-center flex items-center justify-center gap-2 text-sm w-full sm:w-auto min-h-[44px] relative z-10"
                        style={{
                          color: 'white !important',
                          backgroundColor: '#d97706 !important',
                          border: 'none',
                          outline: 'none'
                        }}
                      >
                        <span className="text-lg" style={{ color: 'white !important' }}>✉️</span>
                        <span className="whitespace-nowrap font-medium" style={{ color: 'white !important' }}>Write Me</span>
                      </button>

                      <MailingListButton
                        creatorId={user.id}
                        creatorName={user.name}
                        customUrl={user.custom_url}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('all')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'all'
                      ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📋</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">All ({diaryEntries.length + audioPosts.length + projects.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('diary')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'diary'
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📖</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Diary ({diaryEntries.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('books')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'books'
                      ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📚</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Books ({projects.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('audio')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'audio'
                      ? 'text-orange-600 border-b-2 border-orange-600 bg-orange-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">🎵</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Audio ({audioPosts.length})</span>
                </button>
              </div>

              {/* Sort Controls */}
              <div className="flex items-center gap-2 px-4 py-2 sm:py-0">
                <span className="text-sm text-gray-500">Sort:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'popular')}
                  className="text-sm border border-gray-300 rounded px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                  <option value="popular">Popular</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-4 sm:p-6">
            {activeTab === 'all' && (
              <UnifiedTimelineSection
                diaryEntries={entriesWithPhotos}
                audioPosts={audioPosts}
                projects={projects}
                hasActiveSubscription={hasActiveSubscription}
                writerName={user.name}
                writerId={user.id}
                isOwnProfile={isOwnProfile}
                user={user}
                currentUserId={currentUserId}
                sortBy={sortBy}
              />
            )}

            {activeTab === 'diary' && (
              <DiaryEntriesSection
                entries={entriesWithPhotos}
                hasActiveSubscription={hasActiveSubscription}
                writerName={user.name}
                writerId={user.id}
                isOwnProfile={isOwnProfile}
                user={user}
                sortBy={sortBy}
              />
            )}

            {activeTab === 'books' && (
              <BookProjectsSection
                projects={projects}
                writerName={user.name}
                isOwnProfile={isOwnProfile}
                sortBy={sortBy}
              />
            )}

            {activeTab === 'audio' && (
              <AudioPostsSection
                audioPosts={audioPosts}
                writerName={user.name}
                isOwnProfile={isOwnProfile}
                currentUserId={currentUserId}
                sortBy={sortBy}
              />
            )}
          </div>
        </div>

        {/* Recommended Creators Section */}
        {isOwnProfile && (
          <div className="bg-white rounded-xl shadow-sm p-6 mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-serif text-gray-800">Recommended Creators</h3>
              <button
                onClick={() => setShowRecommendationModal(true)}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                {recommendedCreators.length > 0 ? 'Edit' : 'Add Recommendations'}
              </button>
            </div>

            {recommendedCreators.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {recommendedCreators.map((creator) => (
                <div key={creator.id} className="bg-gray-50 rounded-lg p-4 text-center">
                  {/* Avatar */}
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center mx-auto mb-3 overflow-hidden">
                    {creator.profile_picture_url || creator.avatar ? (
                      <img
                        src={creator.profile_picture_url || creator.avatar}
                        alt={creator.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-600">
                        {creator.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  {/* Name */}
                  <Link href={`/u/${creator.id}`}>
                    <h4 className="font-medium text-gray-800 mb-2 truncate hover:text-blue-600 transition-colors">
                      {creator.name}
                    </h4>
                  </Link>

                  {/* Bio - 5 words max */}
                  <p className="text-xs text-gray-600 text-center mb-3 h-8 flex items-center justify-center">
                    {creator.bio ?
                      `${creator.bio.split(' ').slice(0, 5).join(' ')}${creator.bio.split(' ').length > 5 ? '...' : ''}`
                      : 'No bio'
                    }
                  </p>

                  {/* Follow Button */}
                  <FollowButton
                    writerId={creator.id}
                    writerName={creator.name}
                    initialIsFollowing={false}
                  />
                </div>
              ))}
            </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⭐</span>
                </div>
                <h4 className="text-gray-800 font-medium mb-2">No Recommended Creators Yet</h4>
                <p className="text-gray-500 text-sm mb-4">
                  Showcase creators you love and want to recommend to your readers
                </p>
                <button
                  onClick={() => setShowRecommendationModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  Add Recommendations
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>

    {/* Recommendation Modal */}
    {showRecommendationModal && (
      <RecommendationModal
        onClose={() => {
          setShowRecommendationModal(false)
          // The useEffect will automatically refresh when showRecommendationModal changes
        }}
      />
    )}

    {/* Write Message Modal */}
    {showWriteMessageModal && (
      <WriteMessageModal
        isOpen={showWriteMessageModal}
        onClose={() => setShowWriteMessageModal(false)}
        recipientId={user.id}
        recipientName={user.name || 'this writer'}
      />
    )}
  </>
  )
}

// Diary Entries Section Component - Uniform Grid Layout
function DiaryEntriesSection({
  entries,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile,
  user,
  sortBy
}: {
  entries: DiaryEntry[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
  user: any
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  if (entries.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📔</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Diary Entries Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries yet. Check back soon!
        </p>
      </div>
    )
  }

  // Sort entries based on sortBy parameter
  const sortedEntries = [...entries].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return (b.view_count || 0) - (a.view_count || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  // Separate pinned and regular entries
  const pinnedEntries = sortedEntries.filter((entry: any) => entry.is_pinned === true)
  const regularEntries = sortedEntries.filter((entry: any) => entry.is_pinned !== true)

  return (
    <div className="space-y-8">
      {/* Pinned Entry - Featured */}
      {pinnedEntries.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <span className="text-yellow-500">📌</span>
            Start Here
          </h3>
          <DiaryEntryCard
            entry={pinnedEntries[0]}
            hasAccess={pinnedEntries[0].is_free || hasActiveSubscription || isOwnProfile}
            featured={true}
            user={user}
          />
        </div>
      )}

      {/* Regular Entries Grid - Compact & Uniform */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
        {regularEntries.map((entry) => (
          <DiaryEntryCard
            key={entry.id}
            entry={entry}
            hasAccess={entry.is_free || hasActiveSubscription || isOwnProfile}
            featured={false}
            user={user}
          />
        ))}
      </div>
    </div>
  )
}

// Individual Diary Entry Card Component
function DiaryEntryCard({
  entry,
  hasAccess,
  featured = false,
  user
}: {
  entry: DiaryEntry
  hasAccess: boolean
  featured?: boolean
  user: any
}) {
  // Get preview text (first 120 characters)
  const cleanText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces

  const previewText = cleanText.substring(0, 120).trim()
  const wordCount = cleanText.split(/\s+/).length
  const previewWords = previewText.split(/\s+/).length
  const remainingWords = wordCount - previewWords
  const readTime = Math.ceil(wordCount / 200)
  const firstPhoto = entry.photos?.[0]
  const firstVideo = entry.videos?.[0]
  const totalViews = (entry.view_count || 0) + (entry.videos?.reduce((sum, video) => sum + (video.view_count || 0), 0) || 0)

  return (
    <Link href={`/d/${entry.id}`} className="group block h-full">
      <div className={`bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col ${
        featured ? 'sm:col-span-2 lg:col-span-3' : ''
      }`}>

        {/* Image/Video Section - Square Aspect Ratio */}
        <div className="relative bg-gradient-to-br from-purple-100 to-blue-100 aspect-square">
          {firstVideo ? (
            <VideoThumbnail
              videoUrl={firstVideo.r2_public_url}
              customThumbnailUrl={firstVideo.custom_thumbnail_url}
              alt={entry.title}
              className="w-full h-full object-cover"
              timeInSeconds={1}
              showPlayButton={true}
              playButtonSize="md"
            />
          ) : firstPhoto ? (
            <img
              src={firstPhoto.url}
              alt={firstPhoto.alt_text || entry.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="opacity-50 text-xl">📝</span>
            </div>
          )}

          {/* Status Badges */}
          <div className="absolute top-1.5 right-1.5 flex gap-1">
            {entry.is_free ? (
              <span className="bg-green-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                FREE
              </span>
            ) : (
              <span className="bg-purple-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                ${((user as any)?.price_monthly / 100)?.toFixed(2)}/mo
              </span>
            )}
            {(entry as any).is_pinned === true && (
              <span className="bg-yellow-500/90 text-white text-xs px-1 py-0.5 rounded-full font-medium">
                📌
              </span>
            )}
          </div>

          {/* Lock Overlay for Paid Content */}
          {!hasAccess && (
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
              <div className="bg-white/90 rounded-full p-2">
                <svg className="w-4 h-4 text-gray-700" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          )}
        </div>

        {/* Content Section - Compact */}
        <div className={`p-3 ${featured ? 'sm:p-4' : ''} flex-1 flex flex-col`}>
          {/* Card Header */}
          <div className="flex items-start justify-between mb-1.5">
            <h3 className={`font-serif text-gray-800 line-clamp-1 group-hover:text-purple-600 transition-colors ${
              featured ? 'text-lg sm:text-xl md:text-2xl' : 'text-sm font-semibold'
            }`} title={entry.title}>
              {entry.title}
            </h3>
            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
              {formatDate(entry.created_at).split(',')[0]}
            </span>
          </div>

          {/* Card Description - Compact */}
          <p className={`text-gray-600 line-clamp-2 mb-auto ${
            featured ? 'text-sm' : 'text-xs'
          }`}>
            {previewText}
            {entry.body_md.length > 120 && `... (+${remainingWords} more words)`}
          </p>

          {/* Stats and Button - Compact Bottom Section */}
          <div className="mt-2">
            {/* Stats Row - Clean and Spacious */}
            <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
              <span>{wordCount > 1000 ? `${Math.round(wordCount/1000)}k` : wordCount} words</span>

              {/* View Count */}
              {totalViews > 0 && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                    </svg>
                    <span>{formatViewCount(totalViews)}</span>
                  </span>
                </>
              )}
            </div>

            {/* Read Button - Compact */}
            <button className={`w-full bg-purple-600 text-white py-2 rounded-md font-medium hover:bg-purple-700 transition-colors text-xs ${
              featured ? 'text-sm py-2.5' : ''
            }`}>
              Read Story
            </button>
          </div>
        </div>
      </div>
    </Link>
  )
}

// Book Projects Section Component
function BookProjectsSection({
  projects,
  writerName,
  isOwnProfile,
  sortBy
}: {
  projects: Project[]
  writerName: string
  isOwnProfile: boolean
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  if (projects.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📚</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Book Projects Yet</h3>
        <p className="text-gray-600 font-serif">
          {isOwnProfile ? "Ready to publish your first book?" : `${writerName} hasn't published any book projects yet. Check back soon!`}
        </p>
      </div>
    )
  }

  // Sort projects based on sortBy parameter
  const sortedProjects = [...projects].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        // For books, we can sort by total_words as a proxy for popularity
        return (b.total_words || 0) - (a.total_words || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
      {sortedProjects.map((project) => (
        <Link
          key={project.id}
          href={`/projects/${project.id}`}
          className="group block h-full"
        >
          <div className="bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col">

            {/* Book Cover - Full visibility with proper aspect ratio */}
            <div className="aspect-[10/16] bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
              {project.cover_image_url ? (
                <img
                  src={project.cover_image_url}
                  alt={project.title}
                  className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-4xl opacity-50">📖</span>
                </div>
              )}

              {/* Status Badge */}
              {project.is_complete && (
                <div className="absolute top-1.5 right-1.5">
                  <span className="bg-green-500/90 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                    Complete
                  </span>
                </div>
              )}
            </div>

            {/* Project Info - Compact */}
            <div className="p-3 flex-1 flex flex-col">
              {/* Mobile: Price where author name was */}
              <div className="sm:hidden mb-1.5">
                <span className="text-sm font-semibold text-blue-600">
                  {project.price_amount === 0 ? 'Free' : `$${(project.price_amount / 100).toFixed(2)}`}
                </span>
              </div>

              {/* Title */}
              <h3 className="font-serif text-sm font-semibold text-gray-800 mb-1 line-clamp-1 group-hover:text-blue-600 transition-colors" title={project.title}>
                {project.title}
              </h3>

              {/* Author under title */}
              <div className="mb-2">
                <span className="text-xs text-gray-600">by {writerName}</span>
              </div>

              {/* Synopsis - Extended space */}
              <p className="text-xs text-gray-600 mb-auto line-clamp-3 flex-1">
                {project.description || "This book is a work in progress and does not yet have a description"}
              </p>

              <div className="mt-3">
                {/* Stats Row */}
                <div className="flex items-center gap-2 text-xs text-gray-500 mb-2 flex-wrap">
                  {(project.sales_count && project.sales_count > 0) && (
                    <>
                      <span>{project.sales_count} readers</span>
                      <span>•</span>
                    </>
                  )}
                  <span>{project.total_words > 1000 ? `${Math.round(project.total_words/1000)}k` : project.total_words} words</span>
                  {project.genre && (
                    <>
                      <span>•</span>
                      <span>{project.genre}</span>
                    </>
                  )}
                  {project.bestseller_rank && (
                    <>
                      <span>•</span>
                      <span className="text-purple-600">#{project.bestseller_rank}</span>
                    </>
                  )}
                </div>

                {/* Desktop: Price */}
                <div className="hidden sm:flex items-center justify-between mb-3">
                  <div className="text-xs font-medium text-blue-600">
                    {project.price_amount === 0 ? 'Free' : `$${(project.price_amount / 100).toFixed(2)}`}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  <button
                    onClick={(e) => {
                      e.preventDefault()
                      router.push(`/books/${project.id}`)
                    }}
                    className="w-full bg-gray-100 text-gray-700 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors text-xs"
                  >
                    View Details
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault()
                      router.push(`/books/${project.id}/read`)
                    }}
                    className="w-full bg-blue-600 text-white py-2 rounded-md font-medium hover:bg-blue-700 transition-colors text-xs"
                  >
                    Read Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}

// Audio Posts Section Component
function AudioPostsSection({
  audioPosts,
  writerName,
  isOwnProfile,
  currentUserId,
  sortBy
}: {
  audioPosts: AudioPost[]
  writerName: string
  isOwnProfile: boolean
  currentUserId?: string
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  if (audioPosts.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">🎵</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Audio Posts Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any audio posts yet. Check back soon!
        </p>
      </div>
    )
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error('Failed to toggle love')

      const { loved } = await response.json()

      // Update the audio posts state to reflect the new love count
      // This will trigger a re-render of the AudioPost component
      // The AudioPost component handles its own love count state
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = async (postId: string) => {
    // Audio reply functionality would be handled by the AudioPost component
  }

  // Sort audio posts based on sortBy parameter
  const sortedAudioPosts = [...audioPosts].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return (b.love_count || 0) - (a.love_count || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  return (
    <div className="space-y-6">
      {sortedAudioPosts.map((post) => (
        <AudioPost
          key={post.id}
          post={post}
          currentUserId={currentUserId}
          isFollowing={false}
          onLove={handleAudioLove}
          onReply={handleAudioReply}
        />
      ))}
    </div>
  )
}

// Unified Timeline Section Component - Shows diary entries, audio posts, and books chronologically
function UnifiedTimelineSection({
  diaryEntries,
  audioPosts,
  projects,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile,
  user,
  currentUserId,
  sortBy
}: {
  diaryEntries: DiaryEntry[]
  audioPosts: AudioPost[]
  projects: Project[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
  user: any
  currentUserId?: string
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  const [showComments, setShowComments] = useState<Record<string, boolean>>({})
  const [commentCounts, setCommentCounts] = useState<Record<string, number>>({})
  const supabase = createSupabaseClient()

  // Load comment counts for diary entries and books
  useEffect(() => {
    if (diaryEntries.length > 0) {
      loadDiaryCommentCounts(diaryEntries.map(entry => entry.id))
    }
    if (projects.length > 0) {
      loadBookCommentCounts(projects.map(project => project.id))
    }
  }, [diaryEntries, projects])

  const loadDiaryCommentCounts = async (entryIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('diary_entry_id')
        .in('diary_entry_id', entryIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        entryIds.forEach(id => counts[id] = 0)

        data.forEach(comment => {
          counts[comment.diary_entry_id] = (counts[comment.diary_entry_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading diary comment counts:', err)
    }
  }

  const loadBookCommentCounts = async (bookIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('book_id')
        .in('book_id', bookIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        bookIds.forEach(id => counts[id] = 0)

        data.forEach(comment => {
          counts[comment.book_id] = (counts[comment.book_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading book comment counts:', err)
    }
  }

  const toggleComments = (entryId: string) => {
    setShowComments(prev => ({
      ...prev,
      [entryId]: !prev[entryId]
    }))
  }

  // Combine and sort all posts based on sortBy parameter
  const allPosts = [
    ...diaryEntries.map(entry => ({
      ...entry,
      type: 'diary' as const,
      created_at: entry.created_at,
      popularity_score: (entry.view_count || 0)
    })),
    ...audioPosts.map(post => ({
      ...post,
      type: 'audio' as const,
      created_at: post.created_at,
      popularity_score: (post.love_count || 0) + (post.reply_count || 0)
    })),
    ...projects.map(project => ({
      ...project,
      type: 'book' as const,
      created_at: project.created_at,
      popularity_score: (project.total_words || 0) / 100 // Scale down word count for comparison
    }))
  ].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return b.popularity_score - a.popularity_score
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  if (allPosts.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📋</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Content Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries, audio posts, or books yet. Check back soon!
        </p>
      </div>
    )
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error('Failed to toggle love')

      const { loved } = await response.json()

      // Update the audio posts state to reflect the new love count
      // This will trigger a re-render of the AudioPost component
      // The AudioPost component handles its own love count state
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = async (postId: string) => {
    // Audio reply functionality would be handled by the AudioPost component
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {allPosts.map((post) => {
        if (post.type === 'audio') {
          return (
            <AudioPost
              key={`audio-${post.id}`}
              post={post}
              currentUserId={currentUserId}
              isFollowing={false}
              onLove={handleAudioLove}
              onReply={handleAudioReply}
            />
          )
        } else if (post.type === 'book') {
          // For book projects, show them in a timeline-friendly format with brilliant mobile design
          return (
            <div key={`book-${post.id}`} className="bg-white rounded-xl border border-gray-100 hover:border-purple-200 hover:shadow-lg transition-all duration-300 overflow-hidden group">
              <Link href={`/books/${post.id}`} className="block">
                <div className="p-6">
                  {/* Top row - Badge and date */}
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-xs bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-medium">📚 Book</span>
                    <span className="text-xs text-gray-500">{formatDate(post.created_at)}</span>
                  </div>

                  <div className="flex gap-6">
                    {/* Book Cover */}
                    <div className="w-20 h-28 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg flex-shrink-0 overflow-hidden shadow-sm group-hover:shadow-md transition-all duration-300">
                      {post.cover_image_url ? (
                        <Image
                          src={post.cover_image_url}
                          alt={post.title}
                          width={80}
                          height={112}
                          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-xl opacity-50">📖</span>
                        </div>
                      )}
                    </div>

                    {/* Content - Using full available space */}
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      <h4 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 leading-tight group-hover:text-purple-700 transition-colors duration-300">
                        {post.title}
                      </h4>

                      {/* Author */}
                      <p className="text-sm text-gray-600 mb-2">
                        by {user.name}
                      </p>

                      {/* Synopsis - Sharp, compact, more words */}
                      <p className="text-sm text-gray-700 leading-snug line-clamp-3 -mb-1">
                        {post.description || "This book is a work in progress and does not yet have a description"}
                      </p>
                    </div>
                  </div>

                  {/* Bottom section - Full width, proper spacing */}
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    {/* Stats and genre row */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        {(post.sales_count && post.sales_count > 0) && (
                          <>
                            <span>{post.sales_count} sales</span>
                            <span>•</span>
                          </>
                        )}
                        <span>{post.total_words > 1000 ? `${Math.round(post.total_words/1000)}k` : post.total_words} words</span>
                      </div>
                      {post.genre && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full">
                          {post.genre}
                        </span>
                      )}
                    </div>

                    {/* Price and action - Full width */}
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-purple-600">
                        {post.price_amount === 0 ? 'Free' : `$${(post.price_amount / 100).toFixed(2)}`}
                      </span>

                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          const button = e.currentTarget
                          const spinner = button.querySelector('.loading-spinner')
                          const text = button.querySelector('.button-text')

                          if (spinner && text) {
                            spinner.classList.remove('hidden')
                            text.classList.add('opacity-0')
                          }

                          setTimeout(() => {
                            router.push(`/books/${post.id}`)
                          }, 100)
                        }}
                        className="bg-purple-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors duration-200 relative"
                      >
                        <span className="button-text transition-opacity duration-200">View Book</span>
                        <div className="loading-spinner hidden absolute inset-0 flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Share Button */}
              <div className="px-6 pb-4">
                <ShareButton
                  title={post.title}
                  writerName={user.name}
                  contentType="book"
                  contentId={post.id}
                  variant="compact"
                  url={`${typeof window !== 'undefined' ? window.location.origin : ''}/books/${post.id}`}
                />
              </div>

              {/* Book Comments Section */}
              <CompactBookCommentsSection
                bookId={post.id}
                canComment={!!currentUserId}
                userId={currentUserId}
                isOpen={showComments[post.id] || false}
                onToggle={() => toggleComments(post.id)}
                commentCount={commentCounts[post.id] || 0}
              />
            </div>
          )
        } else {
          // For diary entries, we'll show them in a beautiful, elegant timeline format
          return (
            <div key={`diary-${post.id}`} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <Link href={`/d/${post.id}`} className="block">
                {/* Header with avatar and meta info */}
                <div className="flex items-start gap-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                    {user.avatar || user.profile_picture_url ? (
                      <Image
                        src={(user.avatar || user.profile_picture_url) as string}
                        alt={user.name}
                        width={40}
                        height={40}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm font-serif text-gray-500">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-gray-900 text-sm">{user.name}</h3>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-500">{formatDate(post.created_at)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">📖</span>
                        {!post.is_free && (
                          <span className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded">🔒</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Title - full width */}
                <h4 className="font-semibold text-gray-800 mb-2 line-clamp-2 text-base">{post.title}</h4>

                {/* Text preview - full width */}
                <p className="text-gray-600 line-clamp-3 text-sm mb-2 leading-relaxed">
                  {post.body_md
                    .replace(/[#*`_~]/g, '')
                    .replace(/\n/g, ' ')
                    .substring(0, 160)
                    .trim()}
                  {post.body_md.length > 160 && '...'}
                </p>

                    {/* Media Preview - compact */}
                    {post.photos && post.photos.length > 0 && (
                      <div className="mt-2 mb-2">
                        <Image
                          src={post.photos[0].url}
                          alt={post.photos[0].alt_text || post.title}
                          width={300}
                          height={200}
                          className="w-full h-32 object-cover rounded"
                        />
                      </div>
                    )}

                    {post.videos && post.videos.length > 0 && (
                      <div className="mt-2 mb-2">
                        <div className="relative h-32">
                          <VideoThumbnail
                            videoUrl={post.videos[0].r2_public_url}
                            customThumbnailUrl={post.videos[0].custom_thumbnail_url}
                            alt={post.title}
                            className="w-full h-full object-cover rounded"
                            timeInSeconds={1}
                            showPlayButton={true}
                            playButtonSize="sm"
                          />
                        </div>
                      </div>
                    )}

                    {/* Stats - compact */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-3">
                        <span>{post.body_md.split(' ').length} words</span>
                        {post.view_count && post.view_count > 0 && (
                          <>
                            <span>•</span>
                            <span>{post.view_count} views</span>
                          </>
                        )}
                      </div>
                      <span className="text-purple-600 font-medium">Read →</span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          )
        }
      })}
    </div>
  )
}
