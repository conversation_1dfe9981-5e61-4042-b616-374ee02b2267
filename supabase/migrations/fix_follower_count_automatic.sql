-- Automatic follower count system fix
-- This ensures follower counts work automatically without manual intervention

-- Ensure follower_count column exists with proper default
ALTER TABLE users ADD COLUMN IF NOT EXISTS follower_count INTEGER DEFAULT 0;

-- Update any NULL follower counts to 0
UPDATE users SET follower_count = 0 WHERE follower_count IS NULL;

-- Make follower_count NOT NULL
ALTER TABLE users ALTER COLUMN follower_count SET NOT NULL;
ALTER TABLE users ALTER COLUMN follower_count SET DEFAULT 0;

-- Drop existing trigger and function to recreate them properly
DROP TRIGGER IF EXISTS update_follower_count_trigger ON follows;
DROP FUNCTION IF EXISTS update_follower_count();

-- Create the follower count update function with proper error handling
CREATE OR REPLACE FUNCTION update_follower_count()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- When someone follows a user, increment their follower count
        UPDATE public.users 
        SET follower_count = follower_count + 1 
        WHERE id = NEW.writer_id;
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- When someone unfollows a user, decrement their follower count (never below 0)
        UPDATE public.users 
        SET follower_count = GREATEST(follower_count - 1, 0) 
        WHERE id = OLD.writer_id;
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER update_follower_count_trigger
    AFTER INSERT OR DELETE ON follows
    FOR EACH ROW EXECUTE FUNCTION update_follower_count();

-- Initialize all follower counts based on current follows
UPDATE users SET follower_count = (
    SELECT COUNT(*) 
    FROM follows 
    WHERE writer_id = users.id
);

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_follows_writer_id ON follows(writer_id);
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_users_follower_count ON users(follower_count);

-- Verify the setup
SELECT 
    'Follower count system is now automatic!' as message,
    COUNT(*) as total_users,
    SUM(follower_count) as total_followers,
    COUNT(*) FILTER (WHERE follower_count > 0) as users_with_followers
FROM users;
