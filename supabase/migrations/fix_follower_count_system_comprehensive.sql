-- Comprehensive fix for follower count system
-- This ensures follower counts are always accurate and update in real-time

-- Ensure follower_count column exists
ALTER TABLE users ADD COLUMN IF NOT EXISTS follower_count INTEGER DEFAULT 0;

-- Drop existing trigger and function to recreate them
DROP TRIGGER IF EXISTS update_follower_count_trigger ON follows;
DROP FUNCTION IF EXISTS update_follower_count();

-- Create the follower count update function
CREATE OR REPLACE FUNCTION update_follower_count()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- When someone follows a user, increment their follower count
        UPDATE public.users 
        SET follower_count = follower_count + 1 
        WHERE id = NEW.writer_id;
        
        -- Log the update for debugging
        RAISE NOTICE 'Incremented follower count for user %', NEW.writer_id;
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- When someone unfollows a user, decrement their follower count
        UPDATE public.users 
        SET follower_count = GREATEST(follower_count - 1, 0) 
        WHERE id = OLD.writer_id;
        
        -- Log the update for debugging
        RAISE NOTICE 'Decremented follower count for user %', OLD.writer_id;
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER update_follower_count_trigger
    AFTER INSERT OR DELETE ON follows
    FOR EACH ROW EXECUTE FUNCTION update_follower_count();

-- Function to manually sync all follower counts (in case they get out of sync)
CREATE OR REPLACE FUNCTION sync_all_follower_counts()
RETURNS TEXT
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Update all follower counts based on actual follows
    UPDATE public.users 
    SET follower_count = (
        SELECT COUNT(*) 
        FROM public.follows 
        WHERE writer_id = users.id
    );
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN format('Updated follower counts for %s users', updated_count);
END;
$$ LANGUAGE plpgsql;

-- Sync all existing follower counts
SELECT sync_all_follower_counts();

-- Create indexes for performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_follows_writer_id ON follows(writer_id);
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_users_follower_count ON users(follower_count);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_follower_count() TO authenticated;
GRANT EXECUTE ON FUNCTION sync_all_follower_counts() TO authenticated;

-- Test the trigger by checking current state
SELECT 
    'Follower count system setup complete!' as status,
    COUNT(*) as total_users,
    SUM(follower_count) as total_follower_count,
    COUNT(*) FILTER (WHERE follower_count > 0) as users_with_followers
FROM users;
